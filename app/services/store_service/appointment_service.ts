import { inject } from '@adonisjs/core'
import ZnAppointment, { EAppointmentStatus } from '#models/store_service/zn_appointment'
import ZnStoreService from '#models/store_service/zn_store_service'
import ZnStorePackage from '#models/store_service/zn_store_package'
import ZnStoreTax from '#models/store_service/zn_store_tax'
import ZnStore from '#models/zn_store'
import { DateTime } from 'luxon'
import { v4 as uuidv4 } from 'uuid'

@inject()
export default class AppointmentService {
  
  private async getModelById(id: string) {
    return await ZnAppointment.findOrFail(id)
  }

  async create(data: any) {
    const {
      storeId,
      customerId,
      startTime,
      endTime,
      notes,
      taxId,
      services = [],
      packages = [],
    } = data

    const appointment = await ZnAppointment.create({
      storeId,
      customerId,
      status: EAppointmentStatus.BOOKED,
      startTime: startTime ? DateTime.fromJSDate(startTime) : null,
      endTime: endTime ? DateTime.fromJSDate(endTime) : null,
      notes,
      taxId,
      subtotal: 0,
      discount: 0,
      taxAmount: 0,
      tipAmount: 0,
      total: 0,
    })

    if (services.length > 0) {
      const storeServices = await ZnStoreService.query()
        .whereIn('id', services)
        .where('storeId', storeId)

      const syncData: Record<string, any> = {}
      for (const service of storeServices) {
        syncData[service.id] = {
          id: uuidv4(),
          price: service.price,
          duration: service.duration,
        }
      }
      await appointment.related('services').sync(syncData)
    }

    if (packages.length > 0) {
      const packagesArray = Array.isArray(packages) ? packages : []
      const packageIds = packagesArray.map((p: any) => (typeof p === 'string' ? p : p.id))
      const priceLookup: Record<string, number> = {}
      for (const p of packagesArray) {
        if (typeof p === 'object' && p) {
          const inferredPrice =
            typeof p.totalPrice === 'number'
              ? p.totalPrice
              : typeof p.price === 'number'
              ? p.price
              : parseFloat(p.totalPrice || p.price || '0') || 0
          if (p.id) priceLookup[p.id] = inferredPrice
        }
      }

      const storePackages = await ZnStorePackage.query()
        .whereIn('id', packageIds)
        .where('storeId', storeId)

      const syncData: Record<string, any> = {}
      for (const pkg of storePackages) {
        const packagePrice = priceLookup[pkg.id] ?? 0
        syncData[pkg.id] = {
          id: uuidv4(),
          price: packagePrice,
        }
      }
      await appointment.related('packages').sync(syncData)
    }

    await this.calculateTotals(appointment.id)

    return this.findById(appointment.id)
  }

  async findById(id: string) {
    const appointment = await ZnAppointment.query()
      .where('id', id)
      .preload('store')
      .preload('customer', (query) => {
        query.preload('avatarMedia')
      })
      .preload('tax')
      .preload('services')
      .preload('packages', (query) => {
        query.preload('services')
      })
      .firstOrFail()

    return this.calculateServiceTimes(appointment)
  }

  private calculateServiceTimes(appointment: any) {
    if (!appointment.startTime) {
      return appointment
    }

    let currentTime = appointment.startTime
    let calculatedServices = []
    let calculatedPackages = []

    if (appointment.services && appointment.services.length > 0) {
      calculatedServices = appointment.services.map((service: any) => {
        const duration = service.$extras?.pivot_duration || service.duration || 0
        const startTime = currentTime
        const endTime = currentTime.plus({ minutes: duration })

        currentTime = endTime

        return {
          ...service.serialize(),
          startTime: startTime.toISO(),
          endTime: endTime.toISO(),
          duration
        }
      })
    }

    if (appointment.packages && appointment.packages.length > 0) {
      calculatedPackages = appointment.packages.map((pkg: any) => {
        const packageStartTime = currentTime
        let packageCurrentTime = currentTime
        let calculatedPackageServices = []

        if (pkg.services && pkg.services.length > 0) {
          calculatedPackageServices = pkg.services.map((service: any) => {
            const duration = service.duration || 0
            const startTime = packageCurrentTime
            const endTime = packageCurrentTime.plus({ minutes: duration })

            packageCurrentTime = endTime

            return {
              ...service.serialize(),
              startTime: startTime.toISO(),
              endTime: endTime.toISO(),
              duration
            }
          })
        }

        const packageEndTime = packageCurrentTime
        currentTime = packageEndTime

        return {
          ...pkg.serialize(),
          startTime: packageStartTime.toISO(),
          endTime: packageEndTime.toISO(),
          services: calculatedPackageServices
        }
      })
    }

    const serializedAppointment = appointment.serialize()
    return {
      ...serializedAppointment,
      services: calculatedServices,
      packages: calculatedPackages
    }
  }

  async getByStore(storeId: string, params: any = {}) {
    const { page = 1, limit = 10, status, startDate, endDate, customerId } = params

    const query = ZnAppointment.query()
      .where('storeId', storeId)
      .preload('customer', (query) => {
        query.preload('avatarMedia')
      })
      .preload('services')
      .preload('packages', (query) => {
        query.preload('services')
      })

    if (status) {
      query.where('status', status)
    }

    if (startDate && endDate) {
      query
        .where(
          'startTime',
          '>=',
          startDate
            ? (DateTime.fromJSDate(new Date(startDate)).toUTC().startOf('day').toJSDate() as any)
            : undefined
        )
        .where(
          'endTime',
          '<=',
          endDate
            ? (DateTime.fromJSDate(new Date(endDate)).toUTC().endOf('day').toJSDate() as any)
            : undefined
        )
    }

    if (customerId) {
      query.where('customerId', customerId)
    }

    const result = await query.orderBy('startTime', 'desc').paginate(page, limit)

    const appointments = result.all()
    const appointmentsWithTimes = appointments.map((appointment: any) => this.calculateServiceTimes(appointment))

    const serializedResult = result.serialize()
    return {
      ...serializedResult,
      data: appointmentsWithTimes
    }
  }

  async getByCustomer(customerId: string, params: any = {}) {
    const { page = 1, limit = 10, status, storeId } = params

    const query = ZnAppointment.query()
      .where('customerId', customerId)
      .preload('store')
      .preload('services')
      .preload('packages', (query) => {
        query.preload('services')
      })

    if (status) {
      query.where('status', status)
    }

    if (storeId) {
      query.where('storeId', storeId)
    }

    const result = await query.orderBy('startTime', 'desc').paginate(page, limit)

    const appointments = result.all()
    const appointmentsWithTimes = appointments.map((appointment: any) => this.calculateServiceTimes(appointment))

    const serializedResult = result.serialize()
    return {
      ...serializedResult,
      data: appointmentsWithTimes
    }
  }

  async update(id: string, data: any) {
    const appointment = await this.getModelById(id)
    const { storeId, customerId, status, startTime, endTime, notes, taxId, services, packages } =
      data

    if (storeId) appointment.storeId = storeId
    if (customerId) appointment.customerId = customerId
    if (status) appointment.status = status
    if (startTime) {
      appointment.startTime = DateTime.fromJSDate(startTime)
    }
    if (endTime) {
      appointment.endTime = DateTime.fromJSDate(endTime)
    }
    if (notes !== undefined) appointment.notes = notes
    if (taxId !== undefined) appointment.taxId = taxId

    await appointment.save()

    if (services !== undefined) {
      if (services.length > 0) {
        const storeServices = await ZnStoreService.query()
          .whereIn('id', services)
          .where('storeId', appointment.storeId)

        const syncData: Record<string, any> = {}
        for (const service of storeServices) {
          syncData[service.id] = {
            id: uuidv4(),
            price: service.price,
            duration: service.duration,
          }
        }
        await appointment.related('services').sync(syncData)
      } else {
        // Clear all services if empty array is passed
        await appointment.related('services').detach()
      }
    }

    if (packages !== undefined) {
      if (packages.length > 0) {
        const packagesArray = Array.isArray(packages) ? packages : []
        const packageIds = packagesArray.map((p: any) => (typeof p === 'string' ? p : p.id))
        const priceLookup: Record<string, number> = {}
        for (const p of packagesArray) {
          if (typeof p === 'object' && p) {
            const inferredPrice =
              typeof p.totalPrice === 'number'
                ? p.totalPrice
                : typeof p.price === 'number'
                ? p.price
                : parseFloat(p.totalPrice || p.price || '0') || 0
            if (p.id) priceLookup[p.id] = inferredPrice
          }
        }

        const storePackages = await ZnStorePackage.query()
          .whereIn('id', packageIds)
          .where('storeId', appointment.storeId)

        const syncData: Record<string, any> = {}
        for (const pkg of storePackages) {
          const packagePrice = priceLookup[pkg.id] ?? 0
          syncData[pkg.id] = {
            id: uuidv4(),
            price: packagePrice,
          }
        }
        await appointment.related('packages').sync(syncData)
      } else {
        // Clear all packages if empty array is passed
        await appointment.related('packages').detach()
      }
    }

    if (services !== undefined || packages !== undefined || taxId !== undefined) {
      await this.calculateTotals(id)
    }

    return this.findById(id)
  }

  async updateStatus(id: string, status: EAppointmentStatus) {
    const appointment = await this.getModelById(id)
    appointment.status = status
    await appointment.save()
    return this.findById(id)
  }

  async delete(id: string) {
    const appointment = await this.getModelById(id)
    await appointment.delete()
    return { success: true }
  }

  async calculateTotals(id: string) {
    const appointment = await this.getModelById(id)
    let subtotal = 0

    const services = await appointment.related('services').query()
    for (const service of services) {
      const servicePrice =
        typeof service.$extras.pivot_price === 'number'
          ? service.$extras.pivot_price
          : parseFloat(service.$extras.pivot_price || '0')
      subtotal += servicePrice || 0
    }

    const packages = await appointment.related('packages').query().preload('services')
    for (const pkg of packages) {
      let packagePrice = 0
      if (pkg.$extras.pivot_price) {
        packagePrice = typeof pkg.$extras.pivot_price === 'number'
          ? pkg.$extras.pivot_price
          : parseFloat(pkg.$extras.pivot_price || '0')
      } else {
        packagePrice = pkg.getTotalPrice()
      }
      subtotal += packagePrice || 0
    }

    let taxAmount = 0
    if (appointment.taxId) {
      const tax = await ZnStoreTax.find(appointment.taxId as string)
      if (tax) {
        taxAmount = (subtotal * tax.value) / 100
      }
    }

    const roundedSubtotal = parseFloat(subtotal.toFixed(2))
    const roundedTaxAmount = parseFloat(taxAmount.toFixed(2))
    const roundedTotal = parseFloat((roundedSubtotal + roundedTaxAmount).toFixed(2))

    appointment.subtotal = roundedSubtotal
    appointment.taxAmount = roundedTaxAmount
    appointment.total = roundedTotal

    await appointment.save()
    return appointment
  }

  async getNextAvailableDay({
    storeId,
    serviceIds = [],
    packageIds = [],
    fromDate,
  }: {
    storeId: string
    serviceIds?: string[]
    packageIds?: string[]
    fromDate?: string
  }): Promise<string | null> {
    const store = await ZnStore.findOrFail(storeId)
    const workingHoursRaw = store.workingHour
    const timezone = store.timezone || 'UTC'
    let workingHours: any[] = []
    try {
      workingHours =
        typeof workingHoursRaw === 'string' ? JSON.parse(workingHoursRaw) : workingHoursRaw
    } catch {
      workingHours = []
    }

    let totalDuration = 0
    if (serviceIds.length > 0) {
      const services = await ZnStoreService.query().whereIn('id', serviceIds)
      totalDuration += services.reduce((sum, s) => sum + (s.duration || 0), 0)
    }
    if (packageIds.length > 0) {
      for (const packageId of packageIds) {
        const pkg = await ZnStorePackage.findOrFail(packageId)
        await pkg.load('services')
        totalDuration += pkg.services.reduce((sum: number, s: any) => sum + (s.duration || 0), 0)
      }
    }
    // If no services or packages selected, use default duration of 60 minutes
    if (totalDuration === 0) {
      totalDuration = 60
    }

    let searchDate = fromDate
      ? DateTime.fromISO(fromDate, { zone: timezone })
      : DateTime.now().setZone(timezone)
    searchDate = searchDate.startOf('day')
    const maxDays = 30
    for (let i = 0; i < maxDays; i++) {
      const dayName = searchDate.toFormat('cccc')
      const wh = workingHours.find((h: any) => h.name?.toLowerCase() === dayName.toLowerCase())
      if (!wh || !wh.isOpen) {
        searchDate = searchDate.plus({ days: 1 })
        continue
      }
      const open = DateTime.fromFormat(wh.from, 'HH:mm', { zone: timezone })
      const close = DateTime.fromFormat(wh.to, 'HH:mm', { zone: timezone })
      if (!open.isValid || !close.isValid) {
        searchDate = searchDate.plus({ days: 1 })
        continue
      }
      const dayStart = searchDate.set({
        hour: open.hour,
        minute: open.minute,
        second: 0,
        millisecond: 0,
      })
      const dayEnd = searchDate.set({
        hour: close.hour,
        minute: close.minute,
        second: 0,
        millisecond: 0,
      })
      const appointments = await ZnAppointment.query()
        .where('storeId', storeId)
        .whereIn('status', ['booked', 'confirmed', 'checked-in', 'in-service', 'completed'])
        .where('startTime', '>=', dayStart.toUTC().toISO() || '')
        .where('endTime', '<=', dayEnd.toUTC().toISO() || '')
        .orderBy('startTime', 'asc')

      let slotStart = dayStart
      let slotEnd = slotStart.plus({ minutes: totalDuration })
      let found = false
      while (slotEnd <= dayEnd) {
        const overlap = appointments.some((appt: any) => {
          const apptStart = DateTime.fromJSDate(appt.startTime, { zone: timezone })
          const apptEnd = DateTime.fromJSDate(appt.endTime, { zone: timezone })
          return apptEnd > slotStart && apptStart < slotEnd
        })
        if (!overlap) {
          found = true
          break
        }
        slotStart = slotStart.plus({ minutes: 15 })
        slotEnd = slotStart.plus({ minutes: totalDuration })
      }
      if (found) {
        return searchDate.toISODate()
      }
      searchDate = searchDate.plus({ days: 1 })
    }
    return null
  }

  async getAvailableSlots({
    storeId,
    serviceIds = [],
    packageIds = [],
    date,
  }: {
    storeId: string
    serviceIds?: string[]
    packageIds?: string[]
    date: string
  }): Promise<{ startTime: string; endTime: string }[]> {
    const store = await ZnStore.findOrFail(storeId)
    const workingHoursRaw = store.workingHour
    const timezone = store.timezone || 'UTC'
    let workingHours: any[] = []
    try {
      workingHours =
        typeof workingHoursRaw === 'string' ? JSON.parse(workingHoursRaw) : workingHoursRaw
    } catch {
      workingHours = []
    }

    let totalDuration = 0
    if (serviceIds.length > 0) {
      const services = await ZnStoreService.query().whereIn('id', serviceIds)
      totalDuration += services.reduce((sum, s) => sum + (s.duration || 0), 0)
    }
    if (packageIds.length > 0) {
      for (const packageId of packageIds) {
        const pkg = await ZnStorePackage.findOrFail(packageId)
        await pkg.load('services')
        totalDuration += pkg.services.reduce((sum: number, s: any) => sum + (s.duration || 0), 0)
      }
    }
    // If no services or packages selected, use default duration of 60 minutes
    if (totalDuration === 0) {
      totalDuration = 60
    }

    const searchDate = DateTime.fromISO(date, { zone: timezone }).startOf('day')
    if (!searchDate.isValid) return []

    const dayName = searchDate.toFormat('cccc') // e.g., 'Monday'

    const wh = workingHours.find((h: any) => h.name?.toLowerCase() === dayName.toLowerCase())
    if (!wh || !wh.isOpen) {
      return []
    }

    const open = DateTime.fromFormat(wh.from, 'HH:mm', { zone: timezone })
    const close = DateTime.fromFormat(wh.to, 'HH:mm', { zone: timezone })
    if (!open.isValid || !close.isValid) {
      return []
    }

    const dayStart = searchDate.set({
      hour: open.hour,
      minute: open.minute,
      second: 0,
      millisecond: 0,
    })
    const dayEnd = searchDate.set({
      hour: close.hour,
      minute: close.minute,
      second: 0,
      millisecond: 0,
    })

    const appointments = await ZnAppointment.query()
      .where('storeId', storeId)
      .whereIn('status', ['booked', 'confirmed', 'checked-in', 'in-service', 'completed'])
      .where('startTime', '>=', dayStart.toUTC().toISO() || '')
      .where('startTime', '<', dayEnd.toUTC().toISO() || '')
      .orderBy('startTime', 'asc')

    const availableSlots: { startTime: string; endTime: string }[] = []
    let slotStart = dayStart
    let slotEnd = slotStart.plus({ minutes: totalDuration })

    while (slotEnd <= dayEnd) {
      const overlap = appointments.some((appt: any) => {
        const apptStart = DateTime.fromJSDate(appt.startTime).setZone(timezone)
        const apptEnd = DateTime.fromJSDate(appt.endTime).setZone(timezone)
        return apptEnd > slotStart && apptStart < slotEnd
      })

      if (!overlap) {
        availableSlots.push({
          startTime: slotStart.toISO() || '',
          endTime: slotEnd.toISO() || '',
        })
      }

      slotStart = slotStart.plus({ minutes: 15 })
      slotEnd = slotStart.plus({ minutes: totalDuration })
    }

    return availableSlots
  }
}
